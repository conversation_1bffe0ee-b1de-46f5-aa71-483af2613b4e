'use client';

import { forwardRef, HTMLAttributes } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

const cardVariants = cva(
  "rounded-lg border bg-white shadow-sm transition-shadow",
  {
    variants: {
      variant: {
        default: "border-gray-200",
        elevated: "border-gray-200 shadow-md hover:shadow-lg",
        outlined: "border-2 border-gray-300",
        ghost: "border-transparent shadow-none",
      },
      padding: {
        none: "p-0",
        sm: "p-3",
        md: "p-4 sm:p-6",
        lg: "p-6 sm:p-8",
      },
      interactive: {
        true: "cursor-pointer hover:shadow-md transition-shadow",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      padding: "md",
      interactive: false,
    },
  }
);

export interface CardProps
  extends HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, padding, interactive, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cardVariants({ variant, padding, interactive, className })}
        {...props}
      />
    );
  }
);

Card.displayName = "Card";

// Card Header Component
export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
}

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className = '', title, subtitle, action, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`flex items-start justify-between space-y-1.5 p-6 pb-4 ${className}`}
        {...props}
      >
        <div className="space-y-1 min-w-0 flex-1">
          {title && (
            <h3 className="text-lg font-semibold leading-none tracking-tight text-gray-900">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-sm text-gray-600">
              {subtitle}
            </p>
          )}
          {children}
        </div>
        {action && (
          <div className="flex-shrink-0 ml-4">
            {action}
          </div>
        )}
      </div>
    );
  }
);

CardHeader.displayName = "CardHeader";

// Card Content Component
export type CardContentProps = HTMLAttributes<HTMLDivElement>;

export const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ className = '', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`px-6 pb-6 ${className}`}
        {...props}
      />
    );
  }
);

CardContent.displayName = "CardContent";

// Card Footer Component
export type CardFooterProps = HTMLAttributes<HTMLDivElement>;

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className = '', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`flex items-center px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg ${className}`}
        {...props}
      />
    );
  }
);

CardFooter.displayName = "CardFooter";

// Stats Card Component
export interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period?: string;
  };
  icon?: React.ReactNode;
  className?: string;
}

export function StatsCard({ title, value, change, icon, className = '' }: StatsCardProps) {
  return (
    <Card variant="elevated" className={className}>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {change && (
              <div className="flex items-center space-x-1">
                <span
                  className={`text-xs font-medium ${
                    change.type === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {change.type === 'increase' ? '↗' : '↘'} {Math.abs(change.value)}%
                </span>
                {change.period && (
                  <span className="text-xs text-gray-500">{change.period}</span>
                )}
              </div>
            )}
          </div>
          {icon && (
            <div className="p-3 bg-blue-100 rounded-lg">
              <div className="w-6 h-6 text-blue-600">
                {icon}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Feature Card Component
export interface FeatureCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
  className?: string;
}

export function FeatureCard({ title, description, icon, action, className = '' }: FeatureCardProps) {
  return (
    <Card variant="elevated" interactive className={className}>
      <CardContent>
        <div className="space-y-4">
          {icon && (
            <div className="p-2 bg-blue-100 rounded-lg w-fit">
              <div className="w-6 h-6 text-blue-600">
                {icon}
              </div>
            </div>
          )}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            <p className="text-gray-600">{description}</p>
          </div>
          {action && (
            <div className="pt-2">
              {action}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Product Card Component
export interface ProductCardProps {
  product: {
    id: string;
    name: string;
    price: number;
    image: string;
    category: string;
    inStock: boolean;
  };
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onView?: (id: string) => void;
  className?: string;
}

export function ProductCard({ product, onEdit, onView, className = '' }: ProductCardProps) {
  return (
    <Card variant="elevated" interactive className={className}>
      <div className="aspect-square bg-gray-100 rounded-t-lg overflow-hidden">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-product.svg';
          }}
        />
      </div>
      <CardContent>
        <div className="space-y-3">
          <div>
            <h3 className="font-semibold text-gray-900 truncate">{product.name}</h3>
            <p className="text-sm text-gray-600">{product.category}</p>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-lg font-bold text-gray-900">
              ${product.price.toFixed(2)}
            </span>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full ${
                product.inStock
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {product.inStock ? 'In Stock' : 'Out of Stock'}
            </span>
          </div>
          <div className="flex space-x-2">
            {onView && (
              <button
                onClick={() => onView(product.id)}
                className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                View
              </button>
            )}
            {onEdit && (
              <button
                onClick={() => onEdit(product.id)}
                className="flex-1 px-3 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors"
              >
                Edit
              </button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default Card;
